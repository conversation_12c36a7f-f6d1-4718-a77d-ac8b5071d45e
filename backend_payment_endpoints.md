# Backend Payment Endpoints Implementation Guide

Basé sur votre collection Postman, voici les endpoints que votre backend Laravel doit implémenter pour l'intégration Chargily :

## 1. Create Payment Endpoint

**URL:** `POST /api/payment/create`
**Headers:** 
- `Authorization: Bearer {token}`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "request_id": "string",
  "amount": "number",
  "currency": "string" // Default: "DZD"
}
```

**Response Success (200/201):**
```json
{
  "success": true,
  "data": {
    "checkout_id": "string",
    "payment_url": "string",
    "status": "pending",
    "amount": "number",
    "currency": "string"
  }
}
```

**Response Error (400/500):**
```json
{
  "success": false,
  "message": "Error message"
}
```

## 2. Payment Return Endpoint

**URL:** `GET /api/payment/back`
**Headers:** 
- `Authorization: Bearer {token}`

**Query Parameters:**
- `checkout_id`: string (required)

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "payment_id": "string",
    "status": "paid|failed|cancelled",
    "amount": "number",
    "currency": "string",
    "paid_at": "datetime|null"
  }
}
```

## 3. Webhook Endpoint

**URL:** `POST /api/payment/webhook`
**Headers:** 
- `Content-Type: application/json`

**Request Body:** (From Chargily)
```json
{
  "checkout_id": "string",
  "status": "paid|failed|cancelled",
  "amount": "number",
  "currency": "string",
  "paid_at": "datetime"
}
```

**Response:**
```json
{
  "success": true
}
```

## 4. Payment Status Endpoint (Optional)

**URL:** `GET /api/payment/status/{payment_id}`
**Headers:** 
- `Authorization: Bearer {token}`

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "payment_id": "string",
    "status": "pending|paid|failed|cancelled",
    "amount": "number",
    "currency": "string",
    "paid_at": "datetime|null"
  }
}
```

## Laravel Controller Example

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\ChargilyPaymentService;

class PaymentController extends Controller
{
    protected $chargilyService;

    public function __construct(ChargilyPaymentService $chargilyService)
    {
        $this->chargilyService = $chargilyService;
    }

    public function create(Request $request)
    {
        $request->validate([
            'request_id' => 'required|string',
            'amount' => 'required|numeric|min:0',
            'currency' => 'string|in:DZD,USD,EUR'
        ]);

        try {
            $payment = $this->chargilyService->createPayment(
                $request->request_id,
                $request->amount,
                $request->currency ?? 'DZD'
            );

            return response()->json([
                'success' => true,
                'data' => $payment
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function back(Request $request)
    {
        $checkoutId = $request->query('checkout_id');
        
        if (!$checkoutId) {
            return response()->json([
                'success' => false,
                'message' => 'Checkout ID is required'
            ], 400);
        }

        try {
            $paymentStatus = $this->chargilyService->getPaymentStatus($checkoutId);
            
            return response()->json([
                'success' => true,
                'data' => $paymentStatus
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function webhook(Request $request)
    {
        try {
            $this->chargilyService->handleWebhook($request->all());
            
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
```

## Routes (routes/api.php)

```php
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/payment/create', [PaymentController::class, 'create']);
    Route::get('/payment/back', [PaymentController::class, 'back']);
});

Route::post('/payment/webhook', [PaymentController::class, 'webhook']);
```

## Notes importantes

1. **Sécurité:** Le webhook ne doit pas nécessiter d'authentification car il vient de Chargily
2. **Validation:** Validez toujours les données du webhook avec la signature Chargily
3. **Idempotence:** Gérez les webhooks dupliqués
4. **Logging:** Loggez toutes les transactions pour le debugging
5. **Base URL:** Utilisez `https://api.rokhsati.yakoub-dev.h-s.cloud/api` comme base URL
