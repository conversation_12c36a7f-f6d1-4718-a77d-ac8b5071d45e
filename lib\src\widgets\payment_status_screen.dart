import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../services/payment_service.dart';
import 'locale_provider.dart';

class PaymentStatusScreen extends StatefulWidget {
  final String checkoutId;
  final String requestId;

  const PaymentStatusScreen({
    super.key,
    required this.checkoutId,
    required this.requestId,
  });

  @override
  State<PaymentStatusScreen> createState() => _PaymentStatusScreenState();
}

class _PaymentStatusScreenState extends State<PaymentStatusScreen> {
  PaymentStatus? _paymentStatus;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkPaymentStatus();
  }

  Future<void> _checkPaymentStatus() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final status = await PaymentService.handlePaymentReturn(widget.checkoutId);
      setState(() {
        _paymentStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;
    final colors = ThemeHelper.getColors(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: colors.backgroundPrimary,
      appBar: AppBar(
        title: Text(
          AppLocalizations.tr(context, 'payment_status') ?? 'Payment Status',
          style: ThemeHelper.getTitleStyle(context),
        ),
        backgroundColor: AppColors.primaryOrange,
        foregroundColor: AppColors.pureWhite,
        elevation: 0,
      ),
      body: _buildBody(context, isArabic, isTablet, colors),
    );
  }

  Widget _buildBody(BuildContext context, bool isArabic, bool isTablet, dynamic colors) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return _buildErrorWidget(context, isTablet);
    }

    if (_paymentStatus != null) {
      return _buildStatusWidget(context, isArabic, isTablet, colors);
    }

    return const SizedBox.shrink();
  }

  Widget _buildErrorWidget(BuildContext context, bool isTablet) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 32 : 24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: isTablet ? 80 : 64,
              color: AppColors.error,
            ),
            SizedBox(height: isTablet ? 24 : 16),
            Text(
              AppLocalizations.tr(context, 'payment_error') ?? 'Payment Error',
              style: ThemeHelper.getTitleStyle(context).copyWith(
                fontSize: isTablet ? 24 : 20,
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            Text(
              _errorMessage ?? 'Unknown error occurred',
              style: ThemeHelper.getBodyStyle(context).copyWith(
                fontSize: isTablet ? 16 : 14,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isTablet ? 32 : 24),
            ElevatedButton(
              onPressed: _checkPaymentStatus,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryOrange,
                foregroundColor: AppColors.pureWhite,
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 32 : 24,
                  vertical: isTablet ? 16 : 12,
                ),
              ),
              child: Text(
                AppLocalizations.tr(context, 'retry') ?? 'Retry',
                style: TextStyle(
                  fontSize: isTablet ? 16 : 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusWidget(BuildContext context, bool isArabic, bool isTablet, dynamic colors) {
    final status = _paymentStatus!;
    
    IconData statusIcon;
    Color statusColor;
    String statusText;

    if (status.isPaid) {
      statusIcon = Icons.check_circle;
      statusColor = AppColors.success;
      statusText = AppLocalizations.tr(context, 'payment_success') ?? 'Payment Successful';
    } else if (status.isFailed) {
      statusIcon = Icons.cancel;
      statusColor = AppColors.error;
      statusText = AppLocalizations.tr(context, 'payment_cancelled') ?? 'Payment Failed';
    } else {
      statusIcon = Icons.pending;
      statusColor = AppColors.warning;
      statusText = AppLocalizations.tr(context, 'payment_pending') ?? 'Payment Pending';
    }

    return Center(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 32 : 24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              statusIcon,
              size: isTablet ? 100 : 80,
              color: statusColor,
            ),
            SizedBox(height: isTablet ? 24 : 16),
            Text(
              statusText,
              style: ThemeHelper.getTitleStyle(context).copyWith(
                fontSize: isTablet ? 28 : 24,
                color: statusColor,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            Container(
              padding: EdgeInsets.all(isTablet ? 20 : 16),
              decoration: BoxDecoration(
                color: colors.card,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: statusColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  _buildInfoRow(
                    context,
                    'Payment ID',
                    status.paymentId,
                    isTablet,
                  ),
                  SizedBox(height: isTablet ? 12 : 8),
                  _buildInfoRow(
                    context,
                    'Amount',
                    '${status.amount.toStringAsFixed(2)} ${status.currency}',
                    isTablet,
                  ),
                  if (status.paidAt != null) ...[
                    SizedBox(height: isTablet ? 12 : 8),
                    _buildInfoRow(
                      context,
                      'Paid At',
                      status.paidAt!.toString().split('.')[0],
                      isTablet,
                    ),
                  ],
                ],
              ),
            ),
            SizedBox(height: isTablet ? 32 : 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).popUntil((route) => route.isFirst);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryOrange,
                  foregroundColor: AppColors.pureWhite,
                  padding: EdgeInsets.symmetric(
                    vertical: isTablet ? 16 : 12,
                  ),
                ),
                child: Text(
                  AppLocalizations.tr(context, 'back_to_home') ?? 'Back to Home',
                  style: TextStyle(
                    fontSize: isTablet ? 16 : 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value, bool isTablet) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: ThemeHelper.getBodyStyle(context).copyWith(
            fontSize: isTablet ? 14 : 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        Flexible(
          child: Text(
            value,
            style: ThemeHelper.getBodyStyle(context).copyWith(
              fontSize: isTablet ? 14 : 12,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.end,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
