// Exemple d'utilisation du système de paiement Chargily
// Ce fichier montre comment utiliser le PaymentService dans votre application

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'lib/src/services/payment_service.dart';
import 'lib/src/widgets/payment_status_screen.dart';

class PaymentIntegrationExample extends StatefulWidget {
  const PaymentIntegrationExample({super.key});

  @override
  State<PaymentIntegrationExample> createState() => _PaymentIntegrationExampleState();
}

class _PaymentIntegrationExampleState extends State<PaymentIntegrationExample> {
  
  /// Exemple 1: Créer un paiement simple
  Future<void> createSimplePayment() async {
    try {
      final paymentResponse = await PaymentService.createPayment(
        requestId: 'REQ_123456',
        amount: 5000.0, // 5000 DZD
        currency: 'DZD',
      );

      print('Payment created successfully!');
      print('Checkout ID: ${paymentResponse.checkoutId}');
      print('Payment URL: ${paymentResponse.paymentUrl}');

      // Rediriger vers l'URL de paiement
      final Uri paymentUrl = Uri.parse(paymentResponse.paymentUrl);
      if (await canLaunchUrl(paymentUrl)) {
        await launchUrl(paymentUrl, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      print('Payment creation failed: $e');
      // Gérer l'erreur
    }
  }

  /// Exemple 2: Vérifier le statut d'un paiement
  Future<void> checkPaymentStatus(String paymentId) async {
    try {
      final paymentStatus = await PaymentService.getPaymentStatus(paymentId);

      print('Payment Status: ${paymentStatus.status}');
      print('Amount: ${paymentStatus.amount} ${paymentStatus.currency}');
      print('Is Paid: ${paymentStatus.isPaid}');
      print('Is Pending: ${paymentStatus.isPending}');
      print('Is Failed: ${paymentStatus.isFailed}');

      if (paymentStatus.paidAt != null) {
        print('Paid at: ${paymentStatus.paidAt}');
      }
    } catch (e) {
      print('Failed to check payment status: $e');
    }
  }

  /// Exemple 3: Gérer le retour de paiement
  Future<void> handlePaymentReturn(String checkoutId) async {
    try {
      final paymentStatus = await PaymentService.handlePaymentReturn(checkoutId);

      if (paymentStatus.isPaid) {
        // Paiement réussi
        _showSuccessDialog('Payment successful!');
      } else if (paymentStatus.isFailed) {
        // Paiement échoué
        _showErrorDialog('Payment failed or cancelled');
      } else {
        // Paiement en attente
        _showInfoDialog('Payment is still pending');
      }
    } catch (e) {
      _showErrorDialog('Error checking payment: $e');
    }
  }

  /// Exemple 4: Intégration complète avec navigation
  Future<void> processPaymentWithNavigation(String requestId, double amount) async {
    try {
      // Étape 1: Créer le paiement
      final paymentResponse = await PaymentService.createPayment(
        requestId: requestId,
        amount: amount,
      );

      // Étape 2: Rediriger vers Chargily
      final Uri paymentUrl = Uri.parse(paymentResponse.paymentUrl);
      if (await canLaunchUrl(paymentUrl)) {
        await launchUrl(paymentUrl, mode: LaunchMode.externalApplication);

        // Étape 3: Naviguer vers l'écran de statut
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PaymentStatusScreen(
                checkoutId: paymentResponse.checkoutId,
                requestId: requestId,
              ),
            ),
          );
        }
      } else {
        throw Exception('Cannot open payment URL');
      }
    } catch (e) {
      _showErrorDialog('Payment process failed: $e');
    }
  }

  /// Exemple 5: Calculer le montant basé sur le type de demande
  double calculatePaymentAmount(String requestType) {
    switch (requestType.toLowerCase()) {
      case 'building_permit':
        return 5000.0; // 5000 DZD pour permis de construire
      case 'renovation':
        return 3000.0; // 3000 DZD pour rénovation
      case 'extension':
        return 4000.0; // 4000 DZD pour extension
      case 'demolition':
        return 2500.0; // 2500 DZD pour démolition
      default:
        return 2000.0; // Montant par défaut
    }
  }

  /// Exemple 6: Bouton de paiement réutilisable
  Widget buildPaymentButton({
    required String requestId,
    required String requestType,
    required VoidCallback onPressed,
  }) {
    final amount = calculatePaymentAmount(requestType);
    
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: const Icon(Icons.payment),
      label: Text('Pay ${amount.toStringAsFixed(0)} DZD'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      ),
    );
  }

  // Méthodes utilitaires pour les dialogues
  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Success'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showInfoDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Information'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Integration Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Payment Integration Examples',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // Exemple de bouton de paiement
            buildPaymentButton(
              requestId: 'REQ_EXAMPLE_001',
              requestType: 'building_permit',
              onPressed: () => processPaymentWithNavigation(
                'REQ_EXAMPLE_001',
                calculatePaymentAmount('building_permit'),
              ),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () => checkPaymentStatus('PAYMENT_ID_EXAMPLE'),
              child: const Text('Check Payment Status'),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () => handlePaymentReturn('CHECKOUT_ID_EXAMPLE'),
              child: const Text('Handle Payment Return'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Exemple d'utilisation dans un widget existant
class ExistingWidgetWithPayment extends StatelessWidget {
  final Map<String, dynamic> request;

  const ExistingWidgetWithPayment({
    super.key,
    required this.request,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text('Request ID: ${request['id']}'),
            Text('Type: ${request['requestType']}'),
            Text('Status: ${request['status']}'),
            
            if (request['status'] == 'approved') ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => _handlePayment(context),
                icon: const Icon(Icons.payment),
                label: const Text('Pay Fees'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _handlePayment(BuildContext context) async {
    try {
      final requestId = request['id'].toString();
      final requestType = request['requestType'] ?? 'building_permit';
      final amount = _calculateAmount(requestType);

      final paymentResponse = await PaymentService.createPayment(
        requestId: requestId,
        amount: amount,
      );

      final Uri paymentUrl = Uri.parse(paymentResponse.paymentUrl);
      if (await canLaunchUrl(paymentUrl)) {
        await launchUrl(paymentUrl, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  double _calculateAmount(String requestType) {
    switch (requestType.toLowerCase()) {
      case 'building_permit': return 5000.0;
      case 'renovation': return 3000.0;
      case 'extension': return 4000.0;
      default: return 2000.0;
    }
  }
}
