# Guide de Dépannage - Demande de Renouvellement de Permis

## 🔧 Erreurs Courantes et Solutions

### 1. **"Permit not found or you do not have access to this permit"**

#### Causes possibles :
- L'ID du permis n'existe pas dans la base de données
- L'utilisateur n'est pas propriétaire du permis
- Problème de type de données (string vs integer)
- Token d'authentification invalide

#### Solutions :

**A. Vérifier l'ID du permis :**
```dart
// Utiliser le widget de test pour contourner la validation
RenewRequestScreen(
  permitId: 999, // ID de test
  skipValidation: true, // Ignorer la validation
)
```

**B. Vérifier les permis disponibles :**
- Consultez les logs de l'application pour voir les IDs de permis disponibles
- L'application affiche maintenant : "Available permits: [1, 2, 3]"

**C. Utiliser le mode test :**
```dart
// Naviguer vers l'écran de test
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => RenewRequestTestScreen(),
  ),
);
```

### 2. **"Only expired permits can be renewed"**

#### Cause :
Le permis n'est pas encore expiré selon la base de données.

#### Solutions :

**A. Mode test (recommandé) :**
```dart
RenewRequestScreen(
  permitId: yourPermitId,
  skipValidation: true, // Permet de continuer même si non expiré
)
```

**B. Modifier la date d'expiration en base de données :**
```sql
UPDATE permits SET expiryDate = '2023-01-01' WHERE permitID = 1;
```

### 3. **Erreurs d'authentification**

#### Symptômes :
- "Authentication token is missing"
- "Unauthorized access"

#### Solutions :

**A. Vérifier le token :**
```dart
// Le token est automatiquement récupéré depuis FlutterSecureStorage
// Assurez-vous que l'utilisateur est connecté
```

**B. Se reconnecter :**
- Déconnectez-vous et reconnectez-vous à l'application
- Le token sera automatiquement rafraîchi

### 4. **Erreurs de validation de fichier**

#### Symptômes :
- "File size must be less than 5MB"
- "Error reading file"

#### Solutions :

**A. Taille de fichier :**
- Utilisez un fichier PDF de moins de 5MB
- Compressez le PDF si nécessaire

**B. Format de fichier :**
- Seuls les fichiers PDF sont acceptés
- Vérifiez l'extension du fichier

### 5. **Erreurs de serveur**

#### Symptômes :
- Status 500: "Server error"
- Status 422: "Validation failed"

#### Solutions :

**A. Vérifier les logs du serveur :**
- Consultez les logs Laravel pour plus de détails
- Vérifiez la configuration de la base de données

**B. Vérifier les champs requis :**
- renewalDuration (1-24 mois)
- reason (chaîne de caractères)
- renewal_pdf (fichier PDF)

## 🧪 Mode Test

### Utilisation du RenewRequestTestScreen

```dart
import 'package:your_app/src/widgets/RenewRequestTest.dart';

// Dans votre navigation
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => RenewRequestTestScreen(),
  ),
);
```

### Options de test disponibles :

1. **Test avec validation** (bouton bleu)
   - Teste le flux normal avec validation des permis
   - Utilise l'ID de permis 1

2. **Test sans validation** (bouton vert)
   - Ignore la validation des permis
   - Permet de tester la soumission directement
   - Utilise l'ID de permis 999

3. **Test avec ID différent** (bouton violet)
   - Teste avec l'ID de permis 2
   - Utile pour tester différents scénarios

## 🔍 Debugging

### Logs utiles activés :

L'application affiche maintenant des informations de debug :
- Response de l'API : `API Response: {...}`
- ID recherché : `Looking for permit ID: 1`
- Permis disponibles : `Available permits: [1, 2, 3]`
- Permis trouvé : `Found permit: {...}`

### Vérification manuelle :

1. **Vérifier l'API directement :**
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Accept: application/json" \
     https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits
```

2. **Vérifier la base de données :**
```sql
SELECT permitID, expiryDate, isRenewed 
FROM permits 
JOIN permit_requests ON permits.permitID = permit_requests.id 
WHERE permit_requests.userID = YOUR_USER_ID;
```

## 📞 Support

Si les problèmes persistent :
1. Utilisez le mode test pour contourner les validations
2. Consultez les logs de l'application
3. Vérifiez la connectivité réseau
4. Contactez l'équipe de développement avec les logs d'erreur
