import 'package:flutter/material.dart';
import 'lib/src/services/payment_service.dart';

void main() async {
  // Test payment service
  print('Testing Payment Service...');
  
  try {
    // Test payment creation
    final paymentResponse = await PaymentService.createPayment(
      requestId: 'test_request_123',
      amount: 5000.0,
    );
    
    print('Payment created successfully:');
    print('Checkout ID: ${paymentResponse.checkoutId}');
    print('Payment URL: ${paymentResponse.paymentUrl}');
    print('Status: ${paymentResponse.status}');
    print('Amount: ${paymentResponse.amount} ${paymentResponse.currency}');
    
  } catch (e) {
    print('Payment creation failed: $e');
  }
  
  try {
    // Test payment status check
    final paymentStatus = await PaymentService.getPaymentStatus('test_payment_id');
    
    print('\nPayment status check:');
    print('Payment ID: ${paymentStatus.paymentId}');
    print('Status: ${paymentStatus.status}');
    print('Is Paid: ${paymentStatus.isPaid}');
    print('Is Pending: ${paymentStatus.isPending}');
    print('Is Failed: ${paymentStatus.isFailed}');
    
  } catch (e) {
    print('Payment status check failed: $e');
  }
}
