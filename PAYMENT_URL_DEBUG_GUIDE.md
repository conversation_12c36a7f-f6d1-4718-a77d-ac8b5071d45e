# Guide de Débogage - Problème de Lancement d'URL de Paiement

## 🔍 Problème Identifié

L'application reçoit correctement la réponse de l'API de paiement :
```json
{
  "success": true,
  "redirect_url": "http://pay.chargily.dz/test/checkouts/01k07pxt3hj5bqct3mn5g7rk0a/pay",
  "payment_id": 18
}
```

Mais l'URL ne peut pas être lancée avec le message d'erreur :
```
I/UrlLauncher( 4401): component name for  is null
```

## 🛠️ Solutions Implémentées

### 1. Mise à Jour du Service de Paiement

**Fichier :** `lib/src/services/payment_service.dart`

- ✅ Adaptation à la vraie structure de réponse API
- ✅ Support pour `redirect_url` au lieu de `payment_url`
- ✅ Extraction automatique du `checkout_id` depuis l'URL
- ✅ Gestion robuste des erreurs

### 2. Amélioration du Lancement d'URL

**Fichier :** `lib/src/widgets/history_screen.dart`

- ✅ Méthode `_launchPaymentUrl()` avec plusieurs modes de lancement
- ✅ Logs détaillés pour le débogage
- ✅ Fallback avec WebView intégrée
- ✅ Dialogue de copie d'URL en cas d'échec

### 3. Modes de Lancement Testés

1. **LaunchMode.externalApplication** - Navigateur externe
2. **LaunchMode.platformDefault** - Application par défaut
3. **LaunchMode.inAppWebView** - WebView intégrée

## 🔧 Configuration Android Requise

### Permissions dans `android/app/src/main/AndroidManifest.xml`

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

<!-- Dans <application> -->
<queries>
  <intent>
    <action android:name="android.intent.action.VIEW" />
    <data android:scheme="http" />
  </intent>
  <intent>
    <action android:name="android.intent.action.VIEW" />
    <data android:scheme="https" />
  </intent>
</queries>
```

### Configuration Gradle

**Fichier :** `android/app/build.gradle`

```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        targetSdkVersion 34
        // ...
    }
}
```

## 🧪 Tests de Débogage

### 1. Vérifier les Logs

Recherchez ces messages dans les logs :
```
I/flutter: Attempting to launch URL: http://pay.chargily.dz/test/checkouts/...
I/flutter: Parsed URI: http://pay.chargily.dz/test/checkouts/...
I/flutter: URI scheme: http
I/flutter: URI host: pay.chargily.dz
I/flutter: Trying launch mode: LaunchMode.externalApplication
```

### 2. Test Manuel d'URL

Testez l'URL directement dans un navigateur :
```
http://pay.chargily.dz/test/checkouts/01k07pxt3hj5bqct3mn5g7rk0a/pay
```

### 3. Test avec ADB

```bash
adb shell am start -a android.intent.action.VIEW -d "http://pay.chargily.dz/test/checkouts/01k07pxt3hj5bqct3mn5g7rk0a/pay"
```

## 🔄 Solutions Alternatives

### 1. WebView Intégrée

Si le lancement externe échoue, l'application utilise automatiquement une WebView intégrée :

```dart
await launchUrl(
  uri,
  mode: LaunchMode.inAppWebView,
  webViewConfiguration: const WebViewConfiguration(
    enableJavaScript: true,
    enableDomStorage: true,
  ),
);
```

### 2. Copie d'URL

En dernier recours, l'utilisateur peut copier l'URL et l'ouvrir manuellement :

- Dialogue avec l'URL complète
- Bouton "Copy URL" 
- Message de confirmation

### 3. Navigateur Personnalisé

Ajoutez cette dépendance pour un navigateur personnalisé :

```yaml
dependencies:
  flutter_custom_tabs: ^1.0.4
```

```dart
import 'package:flutter_custom_tabs/flutter_custom_tabs.dart';

await launch(
  paymentUrl,
  customTabsOption: CustomTabsOption(
    toolbarColor: AppColors.primaryOrange,
    enableDefaultShare: true,
    enableUrlBarHiding: true,
  ),
);
```

## 🐛 Problèmes Connus et Solutions

### 1. "component name for is null"

**Cause :** Android ne trouve pas d'application pour gérer l'URL HTTP

**Solutions :**
- Ajouter les permissions `QUERY_ALL_PACKAGES`
- Utiliser HTTPS au lieu de HTTP
- Forcer l'utilisation du navigateur par défaut

### 2. URL non reconnue

**Cause :** Le domaine `pay.chargily.dz` n'est pas reconnu

**Solutions :**
- Vérifier la connectivité réseau
- Tester avec une URL connue (google.com)
- Utiliser la WebView intégrée

### 3. Sécurité Android

**Cause :** Android 11+ restrictions sur les requêtes de packages

**Solutions :**
- Ajouter `<queries>` dans AndroidManifest.xml
- Cibler SDK 30+ avec les bonnes permissions

## 📱 Test sur Différents Appareils

### Émulateur Android
```bash
flutter run
# Tester le paiement
# Vérifier les logs
```

### Appareil Physique
```bash
flutter run --release
# Test en mode release
# Vérifier les performances
```

## 🔍 Logs de Débogage Utiles

Activez ces logs pour plus d'informations :

```dart
// Dans _launchPaymentUrl()
print('Device info: ${Platform.operatingSystem}');
print('Can launch URL check: ${await canLaunchUrl(uri)}');
print('Available browsers: ${await availableBrowsers()}'); // Si disponible
```

## 📞 Support Chargily

Si le problème persiste, contactez le support Chargily avec :

1. **URL de test :** L'URL exacte qui ne fonctionne pas
2. **Logs d'erreur :** Les messages d'erreur complets
3. **Environnement :** Version Android, appareil, navigateur
4. **Configuration :** Votre configuration de test/production

## ✅ Checklist de Vérification

- [ ] Permissions Android ajoutées
- [ ] Queries ajoutées dans AndroidManifest.xml
- [ ] URL de test accessible dans un navigateur
- [ ] Logs de débogage activés
- [ ] Test sur appareil physique
- [ ] Test avec différents navigateurs
- [ ] WebView intégrée fonctionne
- [ ] Copie d'URL fonctionne

## 🚀 Prochaines Étapes

1. **Tester** les solutions implémentées
2. **Vérifier** les logs de débogage
3. **Ajuster** la configuration Android si nécessaire
4. **Contacter** le support Chargily si le problème persiste
5. **Documenter** la solution finale qui fonctionne
