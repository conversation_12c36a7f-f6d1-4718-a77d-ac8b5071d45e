import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:http/http.dart' as http;

import '../lib/src/widgets/NewRequestScreen.dart';
import '../lib/src/widgets/locale_provider.dart';
import '../lib/src/config/app_localizations.dart';

// Generate mocks
@GenerateMocks([http.Client])
import 'new_request_screen_test.mocks.dart';

void main() {
  group('NewRequestScreen Tests', () {
    late MockClient mockClient;

    setUp(() {
      mockClient = MockClient();
      // Mock secure storage
      FlutterSecureStorage.setMockInitialValues({
        'auth_token': 'test_token',
      });
    });

    testWidgets('Should show owner fields as read-only when applicant is owner', (WidgetTester tester) async {
      // Mock user profile response
      when(mockClient.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/profile'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(
        '{"success": true, "data": {"name": "John Doe", "phoneNumber": "123456789", "email": "<EMAIL>", "address": "123 Main St"}}',
        200,
      ));

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (_) => LocaleProvider(),
            child: const NewRequestScreen(),
          ),
          localizationsDelegates: const [
            AppLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'),
            Locale('ar'),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Find the checkbox for "applicant is owner"
      final checkbox = find.byType(Checkbox);
      expect(checkbox, findsOneWidget);

      // Tap the checkbox to make applicant the owner
      await tester.tap(checkbox);
      await tester.pumpAndSettle();

      // Verify that owner name field is read-only
      final ownerNameField = find.byKey(const Key('owner_name_field'));
      if (ownerNameField.evaluate().isNotEmpty) {
        final textField = tester.widget<TextFormField>(ownerNameField);
        expect(textField.readOnly, isTrue);
      }
    });

    testWidgets('Should require owner information when applicant is not owner', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (_) => LocaleProvider(),
            child: const NewRequestScreen(),
          ),
          localizationsDelegates: const [
            AppLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en'),
            Locale('ar'),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Ensure checkbox is not checked (applicant is not owner)
      final checkbox = find.byType(Checkbox);
      final checkboxWidget = tester.widget<Checkbox>(checkbox);
      expect(checkboxWidget.value, isFalse);

      // Try to proceed without filling owner information
      // This would trigger validation requiring owner name and phone
    });

    test('Should convert duration from years to months', () {
      // Test the conversion logic
      const durationInYears = 2;
      const expectedMonths = 24;
      
      final actualMonths = durationInYears * 12;
      expect(actualMonths, equals(expectedMonths));
    });

    test('Should use correct request type format', () {
      // Test that request type is sent in correct format
      const requestType = 'individual';
      expect(requestType, equals('individual'));
      expect(requestType, isNot(equals('INDIVIDUAL')));
    });
  });
}
