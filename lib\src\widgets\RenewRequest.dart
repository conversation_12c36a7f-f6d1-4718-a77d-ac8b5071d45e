import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';

class RenewRequestScreen extends StatefulWidget {
  final int? permitId; // Optional permit ID - will auto-select if null
  final Map<String, dynamic>? permitData; // Optional permit data for validation
  final bool skipValidation; // Skip permit validation for testing
  const RenewRequestScreen({
    super.key,
    this.permitId, // No default - will auto-select expired permit
    this.permitData,
    this.skipValidation = false, // Default to false for production
  });

  @override
  _RenewRequestScreenState createState() => _RenewRequestScreenState();
}

class _RenewRequestScreenState extends State<RenewRequestScreen> {
  final List<PlatformFile> _selectedFiles = [];
  final _formKey = GlobalKey<FormState>();
  final _storage = const FlutterSecureStorage();
  String? _renewalDuration = '6';
  bool _isLoading = false;
  bool _isValidatingPermit = false;
  String? _errorMessage;
  int? _selectedPermitId; // Will store the auto-selected or provided permit ID
  Map<String, dynamic>? _selectedPermitData; // Store the selected permit data
  List<Map<String, dynamic>> _availablePermits = []; // Store all available permits
  List<Map<String, dynamic>> _expiredPermits = []; // Store expired permits
  bool _showPermitSelection = false; // Show permit selection UI
  int _retryCount = 0; // Track retry attempts for server errors
  static const int _maxRetries = 2; // Maximum number of retry attempts

  // Constant reason as specified in requirements
  static const String _constantReason = 'Need more time to complete the project';

  @override
  void initState() {
    super.initState();
    if (!widget.skipValidation) {
      _validatePermitEligibility();
    }
  }

  /// Get authentication token from secure storage
  Future<String?> _getAuthToken() async {
    try {
      final token = await _storage.read(key: 'auth_token');
      return token;
    } catch (e) {
      print('Error retrieving auth token: $e');
      return null;
    }
  }

  /// Select a permit for renewal
  void _selectPermit(Map<String, dynamic> permit) {
    setState(() {
      _selectedPermitId = permit['permitID'] is int ? permit['permitID'] : int.parse(permit['permitID'].toString());
      _selectedPermitData = permit;
      _showPermitSelection = false;
      _errorMessage = null;
      _retryCount = 0; // Reset retry count on new permit selection
    });
    print('Selected permit: ${_selectedPermitId} for renewal');
  }

  /// Handle successful renewal request submission
  void _handleSuccessfulSubmission(Map<String, dynamic>? responseData) {
    if (!mounted) return;

    final message = responseData?['message'] ??
        AppLocalizations.tr(context, 'form_submitted_success') ??
        'Renewal request submitted successfully';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 4),
      ),
    );

    setState(() {
      _selectedFiles.clear();
      _renewalDuration = '6';
      _retryCount = 0; // Reset retry count on success
    });

    // Optional: Show additional information from the response
    if (responseData?['data'] != null) {
      final renewalData = responseData!['data'];
      print('Renewal data: $renewalData');
    }

    // Navigate back after a delay
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate successful submission
      }
    });
  }

  /// Validate permit eligibility for renewal
  Future<void> _validatePermitEligibility() async {
    if (!mounted) return;

    setState(() {
      _isValidatingPermit = true;
      _errorMessage = null;
    });

    try {
      final token = await _getAuthToken();
      if (token == null) {
        setState(() {
          _errorMessage = AppLocalizations.tr(context, 'auth_token_missing') ?? 'Authentication token is missing';
          _isValidatingPermit = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        print('API Response: ${response.body}');
        print('DEBUG: Looking for permit ID: ${widget.permitId} (type: ${widget.permitId.runtimeType})');

        if (responseData['success'] == true && responseData['data'] != null) {
          final permits = responseData['data'] as List<dynamic>;
          final permitsList = permits.map((p) => Map<String, dynamic>.from(p)).toList();

          print('Available permits: ${permitsList.map((p) => p['permitID']).toList()}');

          final expiredPermits = permitsList.where((permit) => permit['isExpired'] == true).toList();

          setState(() {
            _availablePermits = permitsList;
            _expiredPermits = expiredPermits;
          });

          if (widget.permitId != null) {
            final selectedPermit = permitsList.firstWhere(
              (permit) => permit['permitID'] == widget.permitId || permit['permitID'].toString() == widget.permitId.toString(),
              orElse: () => <String, dynamic>{},
            );

            if (selectedPermit.isEmpty) {
              setState(() {
                _errorMessage = 'Permit ID ${widget.permitId} not found in your permits. Available permits: ${permitsList.map((p) => p['permitID']).join(', ')}';
              });
              return;
            }

            setState(() {
              _selectedPermitId = selectedPermit['permitID'] is int ? selectedPermit['permitID'] : int.parse(selectedPermit['permitID'].toString());
              _selectedPermitData = selectedPermit;
            });

            print('Selected permit: $selectedPermit');

            final isExpired = selectedPermit['isExpired'] ?? false;
            if (!isExpired && !widget.skipValidation) {
              setState(() {
                _errorMessage = 'Warning: This permit is not expired yet (expires: ${selectedPermit['expiryDate']}). Normally only expired permits can be renewed.';
              });
            }
          } else {
            if (expiredPermits.isNotEmpty) {
              setState(() {
                _showPermitSelection = true;
              });
            } else {
              setState(() {
                _errorMessage = 'No expired permits found for renewal. You have ${permitsList.length} permits, but none are expired yet.';
              });
              return;
            }
          }

          print('Permit validation successful: Permit ${_selectedPermitId} is eligible for renewal');
        } else {
          setState(() {
            _errorMessage = responseData['message'] ?? 'Failed to retrieve permits. Response: ${response.body}';
          });
        }
      } else if (response.statusCode == 401) {
        setState(() {
          _errorMessage = 'Unauthorized access. Please login again.';
        });
      } else {
        setState(() {
          _errorMessage = 'Failed to validate permit eligibility. Status: ${response.statusCode}, Response: ${response.body}';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error validating permit: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isValidatingPermit = false;
        });
      }
    }
  }

  Future<void> _pickFiles() async {
    if (!mounted) return;

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.size > 5 * 1024 * 1024) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.tr(context, 'file_too_large') ?? 'File size must be less than 5MB')),
          );
          return;
        }
        if (file.bytes == null) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.tr(context, 'file_read_error') ?? 'Error reading file')),
          );
          return;
        }

        if (!mounted) return;
        setState(() {
          _selectedFiles.clear();
          _selectedFiles.add(file);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.tr(context, 'file_selected_success') ?? 'File selected successfully')),
        );
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(AppLocalizations.tr(context, 'no_file_selected') ?? 'No file selected')),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${AppLocalizations.tr(context, 'file_pick_error') ?? 'File selection error'}: $e')),
      );
    }
  }

  Future<void> _submitForm() async {
    if (_selectedFiles.isEmpty) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(AppLocalizations.tr(context, 'no_files_uploaded') ?? 'No file uploaded')),
      );
      return;
    }
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final token = await _getAuthToken();
        if (token == null) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.tr(context, 'auth_token_missing') ?? 'Authentication token is missing')),
          );
          setState(() {
            _isLoading = false;
          });
          return;
        }

        final permitIdToUse = _selectedPermitId ?? widget.permitId;
        if (permitIdToUse == null) {
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No permit selected. Please try again.')),
          );
          setState(() {
            _isLoading = false;
          });
          return;
        }

        print('DEBUG: Submitting renewal for permit ID: $permitIdToUse (type: ${permitIdToUse.runtimeType})');

        var request = http.MultipartRequest(
          'POST',
          Uri.parse('https://api.rokhsati.yakoub-dev.h-s.cloud/api/user/permits/$permitIdToUse/renew'),
        );

        // Add headers
        request.headers['Accept'] = 'application/json';
        request.headers['Authorization'] = 'Bearer $token';
        request.headers['Content-Type'] = 'multipart/form-data';

        // Add form fields
        request.fields['renewalDuration'] = _renewalDuration ?? '6';
        request.fields['reason'] = _constantReason;

        // Add the PDF file
        if (_selectedFiles.isNotEmpty) {
          final file = _selectedFiles.first;
          if (file.bytes != null) {
            request.files.add(
              http.MultipartFile.fromBytes(
                'renewal_pdf',
                file.bytes!,
                filename: file.name.endsWith('.pdf') ? file.name : '${file.name}.pdf',
              ),
            );
          } else {
            throw Exception('File bytes are null');
          }
        }

        print('DEBUG: Sending renewal request with fields: ${request.fields}, files: ${request.files.map((f) => f.filename).toList()}, headers: ${request.headers}');

        final response = await request.send();
        final responseBody = await http.Response.fromStream(response);

        print('DEBUG: Response status: ${response.statusCode}, body: ${responseBody.body}, headers: ${response.headers}');

        if (response.statusCode == 201) {
          if (!mounted) return;

          try {
            final jsonResponse = jsonDecode(responseBody.body);
            if (jsonResponse['success'] == true) {
              _handleSuccessfulSubmission(jsonResponse);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(jsonResponse['message'] ?? 'Unexpected response format'),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          } catch (e) {
            _handleSuccessfulSubmission(null);
          }
        } else {
          if (!mounted) return;

          String errorMessage = AppLocalizations.tr(context, 'form_submission_failed') ?? 'Failed to submit renewal request';

          try {
            final jsonResponse = jsonDecode(responseBody.body);

            if (response.statusCode == 500 && jsonResponse['error']?.contains('Disk [permit_renewals] does not have a configured driver') == true) {
              errorMessage = AppLocalizations.tr(context, 'server_file_storage_error') ??
                  'Server error: Unable to store the uploaded file. Please try again later or contact support.';
              if (_retryCount < _maxRetries) {
                _retryCount++;
                print('DEBUG: Retrying submission (attempt ${_retryCount + 1}/$_maxRetries) after 2 seconds');
                await Future.delayed(const Duration(seconds: 2));
                return _submitForm(); // Retry the submission
              }
            } else if (response.statusCode == 422 && jsonResponse['errors'] != null) {
              final errors = jsonResponse['errors'] as Map<String, dynamic>;
              final errorMessages = <String>[];
              errors.forEach((field, messages) {
                if (messages is List) {
                  errorMessages.addAll(messages.cast<String>());
                } else if (messages is String) {
                  errorMessages.add(messages);
                }
              });
              errorMessage = errorMessages.isNotEmpty
                  ? errorMessages.join('\n')
                  : jsonResponse['message'] ?? errorMessage;
            } else if (response.statusCode == 403) {
              errorMessage = 'You can only renew your own permits';
            } else if (response.statusCode == 400) {
              errorMessage = jsonResponse['message'] ?? 'Only expired permits can be renewed';
            } else if (response.statusCode == 401) {
              errorMessage = 'Authentication required. Please login again.';
            } else {
              errorMessage = jsonResponse['message'] ?? errorMessage;
            }
          } catch (_) {
            switch (response.statusCode) {
              case 500:
                errorMessage = AppLocalizations.tr(context, 'server_error') ??
                    'Server error: Unable to process the request. Please try again later or contact support.';
                if (_retryCount < _maxRetries) {
                  _retryCount++;
                  print('DEBUG: Retrying submission (attempt ${_retryCount + 1}/$_maxRetries) after 2 seconds');
                  await Future.delayed(const Duration(seconds: 2));
                  return _submitForm(); // Retry the submission
                }
                break;
              case 403:
                errorMessage = 'You can only renew your own permits';
                break;
              case 400:
                errorMessage = 'Only expired permits can be renewed';
                break;
              case 401:
                errorMessage = 'Authentication required. Please login again.';
                break;
              case 422:
                errorMessage = 'Validation failed. Please check your input.';
                break;
              default:
                errorMessage = '$errorMessage (Status: ${response.statusCode})';
            }
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 7),
            ),
          );
        }
      } catch (e) {
        if (!mounted) return;
        print('DEBUG: Submission error: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocalizations.tr(context, 'form_submission_error') ?? 'Submission error'}: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.locale.languageCode == 'ar';
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundPrimary,
      appBar: AppBar(
        title: Text(
          AppLocalizations.tr(context, 'renew_request'),
          style: ThemeHelper.getTitleStyle(context),
        ),
        backgroundColor: AppColors.primaryOrange,
        foregroundColor: AppColors.pureWhite,
        elevation: 0,
      ),
      body: _isValidatingPermit
          ? const Center(child: CircularProgressIndicator())
          : _showPermitSelection
              ? _buildPermitSelectionWidget()
              : _errorMessage != null && !widget.skipValidation
                  ? _buildErrorWidget()
                  : widget.skipValidation && _errorMessage != null
                      ? _buildWarningWidget()
                      : Form(
                          key: _formKey,
                          child: SingleChildScrollView(
                            padding: Constants.screenPadding,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppLocalizations.tr(context, 'renew_request'),
                                  style: ThemeHelper.getSectionTitleStyle(context),
                                ),
                                const SizedBox(height: Constants.mediumSpacing),
                                if (_selectedPermitData != null) _buildSelectedPermitInfo(),
                                Card(
                                  elevation: Constants.cardTheme.elevation,
                                  shape: Constants.cardTheme.shape,
                                  color: ThemeHelper.getColors(context).card,
                                  child: Padding(
                                    padding: Constants.cardPadding,
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        _buildRequiredDocumentsList(isArabic),
                                        const SizedBox(height: Constants.largeSpacing),
                                        _buildFileUploadSection(isArabic),
                                        const SizedBox(height: Constants.largeSpacing),
                                        _buildRenewalDurationField(isArabic),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(height: Constants.extraLargeSpacing),
                                _buildActionButton(isArabic, context),
                              ],
                            ),
                          ),
                        ),
    );
  }

  Widget _buildPermitSelectionWidget() {
    return Padding(
      padding: Constants.screenPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select a Permit to Renew',
            style: ThemeHelper.getSectionTitleStyle(context),
          ),
          const SizedBox(height: 16),
          Text(
            'You have ${_expiredPermits.length} expired permit(s) that can be renewed:',
            style: ThemeHelper.getBodyStyle(context),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: _expiredPermits.length,
              itemBuilder: (context, index) {
                final permit = _expiredPermits[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    title: Text('Permit ID: ${permit['permitID']}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Project: ${permit['permitRequest']?['projectAddress'] ?? 'N/A'}'),
                        Text('Expired: ${permit['expiryDate']}'),
                        Text('Type: ${permit['permitRequest']?['projectType'] ?? 'N/A'}'),
                      ],
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => _selectPermit(permit),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: Constants.screenPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: Constants.mediumSpacing),
            Text(
              _errorMessage!,
              style: ThemeHelper.getSubtitleStyle(context).copyWith(
                color: AppColors.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: Constants.largeSpacing),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                  _retryCount = 0; // Reset retry count on manual retry
                });
                _validatePermitEligibility();
              },
              style: ThemeHelper.getPrimaryButtonStyle(context),
              child: Text(AppLocalizations.tr(context, 'retry') ?? 'Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarningWidget() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          margin: Constants.screenPadding,
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            border: Border.all(color: Colors.orange),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.warning_amber_outlined,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _errorMessage!,
                  style: ThemeHelper.getSubtitleStyle(context).copyWith(
                    color: Colors.orange[800],
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: Constants.screenPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.tr(context, 'renew_request'),
                    style: ThemeHelper.getSectionTitleStyle(context),
                  ),
                  const SizedBox(height: Constants.mediumSpacing),
                  Card(
                    elevation: Constants.cardTheme.elevation,
                    shape: Constants.cardTheme.shape,
                    color: ThemeHelper.getColors(context).card,
                    child: Padding(
                      padding: Constants.cardPadding,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildRequiredDocumentsList(Provider.of<LocaleProvider>(context).locale.languageCode == 'ar'),
                          const SizedBox(height: Constants.largeSpacing),
                          _buildFileUploadSection(Provider.of<LocaleProvider>(context).locale.languageCode == 'ar'),
                          const SizedBox(height: Constants.largeSpacing),
                          _buildRenewalDurationField(Provider.of<LocaleProvider>(context).locale.languageCode == 'ar'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: Constants.extraLargeSpacing),
                  _buildActionButton(Provider.of<LocaleProvider>(context).locale.languageCode == 'ar', context),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedPermitInfo() {
    if (_selectedPermitData == null) return const SizedBox.shrink();

    final permit = _selectedPermitData!;
    final isExpired = permit['isExpired'] ?? false;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: Constants.mediumSpacing),
      color: isExpired ? Colors.red.withOpacity(0.1) : Colors.green.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isExpired ? Icons.warning_amber : Icons.check_circle,
                  color: isExpired ? Colors.red : Colors.green,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Selected Permit: #${permit['permitID']}',
                  style: ThemeHelper.getSubtitleStyle(context).copyWith(
                    fontWeight: FontWeight.bold,
                    color: isExpired ? Colors.red[800] : Colors.green[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Issue Date: ${permit['issueDate'] ?? 'N/A'}',
              style: ThemeHelper.getSubtitleStyle(context).copyWith(fontSize: 12),
            ),
            Text(
              'Expiry Date: ${permit['expiryDate'] ?? 'N/A'}',
              style: ThemeHelper.getSubtitleStyle(context).copyWith(fontSize: 12),
            ),
            Text(
              'Status: ${isExpired ? 'Expired' : 'Active'}',
              style: ThemeHelper.getSubtitleStyle(context).copyWith(
                fontSize: 12,
                color: isExpired ? Colors.red[700] : Colors.green[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequiredDocumentsList(bool isArabic) {
    final documents = [
      AppLocalizations.tr(context, 'document_id_copy'),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: documents.map((doc) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    top: 6,
                    left: isArabic ? 8 : 0,
                    right: isArabic ? 0 : 8,
                  ),
                  child: Container(
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: AppColors.info,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    doc,
                    style: ThemeHelper.getSubtitleStyle(context),
                    textAlign: isArabic ? TextAlign.right : TextAlign.left,
                  ),
                ),
              ],
            ),
          )).toList(),
    );
  }

  Widget _buildFileUploadSection(bool isArabic) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const Icon(
          Icons.upload_file,
          size: 48,
          color: AppColors.info,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Text(
          AppLocalizations.tr(context, 'upload_file'),
          style: ThemeHelper.getSectionTitleStyle(context).copyWith(fontSize: 18),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.tr(context, 'click_to_select_file'),
          style: ThemeHelper.getSubtitleStyle(context),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: Constants.mediumSpacing),
        Center(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _pickFiles,
            style: ThemeHelper.getSecondaryButtonStyle(context).copyWith(
              minimumSize: WidgetStateProperty.all(const Size(200, 40)),
            ),
            child: Text(AppLocalizations.tr(context, 'select_file_button')),
          ),
        ),
        if (_selectedFiles.isNotEmpty) ...[
          const SizedBox(height: Constants.mediumSpacing),
          Center(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ThemeHelper.getColors(context).backgroundSecondary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    AppLocalizations.tr(context, 'selected_file'),
                    style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.picture_as_pdf,
                        size: 20,
                        color: AppColors.darkGray,
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          _selectedFiles.first.name,
                          style: ThemeHelper.getSubtitleStyle(context),
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 20),
                        onPressed: () => setState(() => _selectedFiles.clear()),
                        color: AppColors.darkGray,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
        const SizedBox(height: 8),
        Text(
          AppLocalizations.tr(context, 'pdf_only'),
          style: ThemeHelper.getSubtitleStyle(context).copyWith(
            fontSize: 12,
            color: ThemeHelper.getColors(context).textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRenewalDurationField(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.tr(context, 'renewal_duration'),
          style: ThemeHelper.getSubtitleStyle(context).copyWith(fontWeight: FontWeight.bold),
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _renewalDuration,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: ['6', '12', '24'].map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text('$value ${AppLocalizations.tr(context, 'months')}'),
            );
          }).toList(),
          onChanged: (String? newValue) {
            setState(() {
              _renewalDuration = newValue;
            });
          },
          validator: (value) {
            if (value == null) {
              return AppLocalizations.tr(context, 'select_renewal_duration');
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildActionButton(bool isArabic, BuildContext context) {
    return Center(
      child: ElevatedButton(
        onPressed: (_isLoading || _selectedFiles.isEmpty) ? null : _submitForm,
        style: ThemeHelper.getPrimaryButtonStyle(context).copyWith(
          minimumSize: WidgetStateProperty.all(const Size(200, 40)),
        ),
        child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: AppColors.pureWhite,
                  strokeWidth: 2,
                ),
              )
            : Text(AppLocalizations.tr(context, 'submit')),
      ),
    );
  }
}